<?php
/**
 * Template da página de administração
 */

if (!defined('ABSPATH')) {
    exit('Acesso direto não permitido');
}
?>

<div class="wrap cpr-admin-wrap">
    <div class="cpr-admin-header">
        <div class="cpr-header-content">
            <div class="cpr-header-title">
                <div class="cpr-icon">
                    <i class="dashicons dashicons-lock"></i>
                </div>
                <div class="cpr-title-text">
                    <h1>Esqueceu Senha</h1>
                    <p>Configure a aparência e comportamento da tela de redefinição de senha personalizada.</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="cpr-admin-container">
        <!-- Navegação por abas -->
        <nav class="cpr-nav-tabs">
            <div class="cpr-nav-wrapper">
                <a href="#general" class="cpr-nav-tab cpr-nav-tab-active" data-tab="general">
                    <span class="cpr-tab-icon dashicons dashicons-admin-generic"></span>
                    <span class="cpr-tab-text">Geral</span>
                </a>
                <a href="#appearance" class="cpr-nav-tab" data-tab="appearance">
                    <span class="cpr-tab-icon dashicons dashicons-admin-appearance"></span>
                    <span class="cpr-tab-text">Aparência</span>
                </a>
                <a href="#texts" class="cpr-nav-tab" data-tab="texts">
                    <span class="cpr-tab-icon dashicons dashicons-editor-textcolor"></span>
                    <span class="cpr-tab-text">Textos</span>
                </a>
                <a href="#email" class="cpr-nav-tab" data-tab="email">
                    <span class="cpr-tab-icon dashicons dashicons-email-alt"></span>
                    <span class="cpr-tab-text">Email</span>
                </a>
                <a href="#advanced" class="cpr-nav-tab" data-tab="advanced">
                    <span class="cpr-tab-icon dashicons dashicons-admin-tools"></span>
                    <span class="cpr-tab-text">Avançado</span>
                </a>
                <a href="#preview" class="cpr-nav-tab" data-tab="preview">
                    <span class="cpr-tab-icon dashicons dashicons-visibility"></span>
                    <span class="cpr-tab-text">Visualizar</span>
                </a>
            </div>
        </nav>
        
        <!-- Formulário principal -->
        <form id="cpr-settings-form" method="post">
            <?php wp_nonce_field('cpr_admin_nonce', 'nonce'); ?>
            
            <!-- Aba Geral -->
            <div class="cpr-tab-content" id="tab-general">
                <div class="cpr-settings-grid">
                    <div class="cpr-setting-group">
                        <div class="cpr-setting-header">
                            <h3>Configurações Gerais</h3>
                        </div>

                        <div class="cpr-setting-item">
                            <label for="theme" class="cpr-setting-label">Tema Padrão</label>
                            <select name="settings[theme]" id="theme" class="cpr-select">
                                <option value="dark" <?php selected($settings['theme'], 'dark'); ?>>Escuro</option>
                                <option value="light" <?php selected($settings['theme'], 'light'); ?>>Claro</option>
                            </select>
                            <span class="cpr-setting-desc">Tema padrão que será carregado inicialmente.</span>
                        </div>

                        <div class="cpr-setting-item">
                            <label for="logo_url" class="cpr-setting-label">URL da Logo</label>
                            <div class="cpr-input-group">
                                <input type="url" name="settings[logo_url]" id="logo_url" value="<?php echo esc_attr($settings['logo_url']); ?>" class="cpr-input" placeholder="https://exemplo.com/logo.png">
                                <button type="button" class="cpr-btn-secondary cpr-upload-button" data-target="logo_url">
                                    <span class="dashicons dashicons-upload"></span> Selecionar
                                </button>
                            </div>
                            <span class="cpr-setting-desc">Logo que aparecerá no topo do formulário. Se vazio, será usado um ícone padrão.</span>
                        </div>

                        <div class="cpr-setting-item">
                            <label class="cpr-setting-label">Página Personalizada</label>
                            <div class="cpr-toggle-wrapper">
                                <label class="cpr-toggle">
                                    <input type="checkbox" name="settings[enable_custom_page]" id="enable_custom_page" value="1" <?php checked($settings['enable_custom_page'], true); ?>>
                                    <span class="cpr-toggle-slider"></span>
                                </label>
                                <span class="cpr-toggle-text">Usar página personalizada em vez da padrão do WordPress</span>
                            </div>
                            <span class="cpr-setting-desc">Quando ativado, redirecionará para uma página personalizada.</span>
                        </div>

                        <div class="cpr-setting-item cpr-conditional" data-condition="enable_custom_page" data-value="1">
                            <label for="custom_page_slug" class="cpr-setting-label">Slug da Página</label>
                            <input type="text" name="settings[custom_page_slug]" id="custom_page_slug" value="<?php echo esc_attr($settings['custom_page_slug']); ?>" class="cpr-input" placeholder="esqueceu-senha">
                            <span class="cpr-setting-desc">Slug da página personalizada (ex: esqueceu-senha).</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Aba Aparência -->
            <div class="cpr-tab-content" id="tab-appearance" style="display: none;">
                <div class="cpr-settings-grid">
                    <!-- Cores Avançadas do Tema Dark -->
                    <div class="cpr-card">
                        <h2>Cores do Tema Dark</h2>

                    <table class="form-table">
                        <tr>
                            <th scope="row">
                                <label for="dark_primary_bg">Fundo Principal (Dark)</label>
                            </th>
                            <td>
                                <div class="cpr-color-picker-wrapper">
                                    <input type="text" name="settings[dark_primary_bg]" id="dark_primary_bg" value="<?php echo esc_attr($settings['dark_primary_bg']); ?>" class="cpr-color-picker" data-default-color="<?php echo esc_attr($settings['dark_primary_bg']); ?>">
                                    <span class="cpr-color-value"><?php echo esc_attr($settings['dark_primary_bg']); ?></span>
                                </div>
                                <p class="description">Cor de fundo da página no tema dark.</p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="dark_card_bg">Fundo do Card (Dark)</label>
                            </th>
                            <td>
                                <input type="text" name="settings[dark_card_bg]" id="dark_card_bg" value="<?php echo esc_attr($settings['dark_card_bg']); ?>" class="cpr-color-picker">
                                <p class="description">Cor de fundo do formulário no tema dark.</p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="dark_text_color">Cor do Texto (Dark)</label>
                            </th>
                            <td>
                                <input type="text" name="settings[dark_text_color]" id="dark_text_color" value="<?php echo esc_attr($settings['dark_text_color']); ?>" class="cpr-color-picker">
                                <p class="description">Cor do texto principal no tema dark.</p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="dark_text_secondary">Texto Secundário (Dark)</label>
                            </th>
                            <td>
                                <input type="text" name="settings[dark_text_secondary]" id="dark_text_secondary" value="<?php echo esc_attr($settings['dark_text_secondary']); ?>" class="cpr-color-picker">
                                <p class="description">Cor do texto secundário no tema dark.</p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="dark_button_bg">Fundo do Botão (Dark)</label>
                            </th>
                            <td>
                                <input type="text" name="settings[dark_button_bg]" id="dark_button_bg" value="<?php echo esc_attr($settings['dark_button_bg']); ?>" class="cpr-color-picker">
                                <p class="description">Cor de fundo do botão no tema dark.</p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="dark_button_text">Texto do Botão (Dark)</label>
                            </th>
                            <td>
                                <input type="text" name="settings[dark_button_text]" id="dark_button_text" value="<?php echo esc_attr($settings['dark_button_text']); ?>" class="cpr-color-picker">
                                <p class="description">Cor do texto do botão no tema dark.</p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="dark_button_hover">Botão Hover (Dark)</label>
                            </th>
                            <td>
                                <input type="text" name="settings[dark_button_hover]" id="dark_button_hover" value="<?php echo esc_attr($settings['dark_button_hover']); ?>" class="cpr-color-picker">
                                <p class="description">Cor do botão ao passar o mouse no tema dark.</p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="dark_input_bg">Fundo do Input (Dark)</label>
                            </th>
                            <td>
                                <input type="text" name="settings[dark_input_bg]" id="dark_input_bg" value="<?php echo esc_attr($settings['dark_input_bg']); ?>" class="cpr-color-picker">
                                <p class="description">Cor de fundo dos campos de entrada no tema dark.</p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="dark_input_border">Borda do Input (Dark)</label>
                            </th>
                            <td>
                                <input type="text" name="settings[dark_input_border]" id="dark_input_border" value="<?php echo esc_attr($settings['dark_input_border']); ?>" class="cpr-color-picker">
                                <p class="description">Cor da borda dos campos no tema dark.</p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="dark_input_focus">Input Focado (Dark)</label>
                            </th>
                            <td>
                                <input type="text" name="settings[dark_input_focus]" id="dark_input_focus" value="<?php echo esc_attr($settings['dark_input_focus']); ?>" class="cpr-color-picker">
                                <p class="description">Cor da borda quando o campo está focado no tema dark.</p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="dark_input_text">Texto do Input (Dark)</label>
                            </th>
                            <td>
                                <input type="text" name="settings[dark_input_text]" id="dark_input_text" value="<?php echo esc_attr($settings['dark_input_text']); ?>" class="cpr-color-picker">
                                <p class="description">Cor do texto digitado nos campos no tema dark.</p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="dark_input_placeholder">Placeholder (Dark)</label>
                            </th>
                            <td>
                                <input type="text" name="settings[dark_input_placeholder]" id="dark_input_placeholder" value="<?php echo esc_attr($settings['dark_input_placeholder']); ?>" class="cpr-color-picker">
                                <p class="description">Cor do texto placeholder nos campos no tema dark.</p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="dark_card_border">Borda do Card (Dark)</label>
                            </th>
                            <td>
                                <input type="text" name="settings[dark_card_border]" id="dark_card_border" value="<?php echo esc_attr($settings['dark_card_border']); ?>" class="cpr-color-picker">
                                <p class="description">Cor da borda do card no tema dark.</p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="dark_card_shadow">Sombra do Card (Dark)</label>
                            </th>
                            <td>
                                <input type="text" name="settings[dark_card_shadow]" id="dark_card_shadow" value="<?php echo esc_attr($settings['dark_card_shadow']); ?>" class="regular-text">
                                <p class="description">Sombra do card no tema dark (ex: 0 4px 20px rgba(0, 0, 0, 0.3)).</p>
                            </td>
                        </tr>

                    </table>
                </div>

                <!-- Cores Avançadas do Tema Light -->
                <div class="cpr-card">
                    <h2>Cores do Tema Light</h2>

                    <table class="form-table">
                        <tr>
                            <th scope="row">
                                <label for="light_primary_bg">Fundo Principal (Light)</label>
                            </th>
                            <td>
                                <input type="text" name="settings[light_primary_bg]" id="light_primary_bg" value="<?php echo esc_attr($settings['light_primary_bg']); ?>" class="cpr-color-picker">
                                <p class="description">Cor de fundo da página no tema light.</p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="light_card_bg">Fundo do Card (Light)</label>
                            </th>
                            <td>
                                <input type="text" name="settings[light_card_bg]" id="light_card_bg" value="<?php echo esc_attr($settings['light_card_bg']); ?>" class="cpr-color-picker">
                                <p class="description">Cor de fundo do formulário no tema light.</p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="light_text_color">Cor do Texto (Light)</label>
                            </th>
                            <td>
                                <input type="text" name="settings[light_text_color]" id="light_text_color" value="<?php echo esc_attr($settings['light_text_color']); ?>" class="cpr-color-picker">
                                <p class="description">Cor do texto principal no tema light.</p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="light_text_secondary">Texto Secundário (Light)</label>
                            </th>
                            <td>
                                <input type="text" name="settings[light_text_secondary]" id="light_text_secondary" value="<?php echo esc_attr($settings['light_text_secondary']); ?>" class="cpr-color-picker">
                                <p class="description">Cor do texto secundário no tema light.</p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="light_button_bg">Fundo do Botão (Light)</label>
                            </th>
                            <td>
                                <input type="text" name="settings[light_button_bg]" id="light_button_bg" value="<?php echo esc_attr($settings['light_button_bg']); ?>" class="cpr-color-picker">
                                <p class="description">Cor de fundo do botão no tema light.</p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="light_button_text">Texto do Botão (Light)</label>
                            </th>
                            <td>
                                <input type="text" name="settings[light_button_text]" id="light_button_text" value="<?php echo esc_attr($settings['light_button_text']); ?>" class="cpr-color-picker">
                                <p class="description">Cor do texto do botão no tema light.</p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="light_button_hover">Botão Hover (Light)</label>
                            </th>
                            <td>
                                <input type="text" name="settings[light_button_hover]" id="light_button_hover" value="<?php echo esc_attr($settings['light_button_hover']); ?>" class="cpr-color-picker">
                                <p class="description">Cor do botão ao passar o mouse no tema light.</p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="light_input_bg">Fundo do Input (Light)</label>
                            </th>
                            <td>
                                <input type="text" name="settings[light_input_bg]" id="light_input_bg" value="<?php echo esc_attr($settings['light_input_bg']); ?>" class="cpr-color-picker">
                                <p class="description">Cor de fundo dos campos de entrada no tema light.</p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="light_input_border">Borda do Input (Light)</label>
                            </th>
                            <td>
                                <input type="text" name="settings[light_input_border]" id="light_input_border" value="<?php echo esc_attr($settings['light_input_border']); ?>" class="cpr-color-picker">
                                <p class="description">Cor da borda dos campos no tema light.</p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="light_input_focus">Input Focado (Light)</label>
                            </th>
                            <td>
                                <input type="text" name="settings[light_input_focus]" id="light_input_focus" value="<?php echo esc_attr($settings['light_input_focus']); ?>" class="cpr-color-picker">
                                <p class="description">Cor da borda quando o campo está focado no tema light.</p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="light_input_text">Texto do Input (Light)</label>
                            </th>
                            <td>
                                <input type="text" name="settings[light_input_text]" id="light_input_text" value="<?php echo esc_attr($settings['light_input_text']); ?>" class="cpr-color-picker">
                                <p class="description">Cor do texto digitado nos campos no tema light.</p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="light_input_placeholder">Placeholder (Light)</label>
                            </th>
                            <td>
                                <input type="text" name="settings[light_input_placeholder]" id="light_input_placeholder" value="<?php echo esc_attr($settings['light_input_placeholder']); ?>" class="cpr-color-picker">
                                <p class="description">Cor do texto placeholder nos campos no tema light.</p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="light_card_border">Borda do Card (Light)</label>
                            </th>
                            <td>
                                <input type="text" name="settings[light_card_border]" id="light_card_border" value="<?php echo esc_attr($settings['light_card_border']); ?>" class="cpr-color-picker">
                                <p class="description">Cor da borda do card no tema light.</p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="light_card_shadow">Sombra do Card (Light)</label>
                            </th>
                            <td>
                                <input type="text" name="settings[light_card_shadow]" id="light_card_shadow" value="<?php echo esc_attr($settings['light_card_shadow']); ?>" class="regular-text">
                                <p class="description">Sombra do card no tema light (ex: 0 4px 20px rgba(0, 0, 0, 0.1)).</p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="button_hover_color">Cor do Botão ao Passar o Mouse</label>
                            </th>
                            <td>
                                <input type="text" name="settings[button_hover_color]" id="button_hover_color" value="<?php echo esc_attr($settings['button_hover_color']); ?>" class="cpr-color-picker">
                                <p class="description">Cor do botão quando o usuário passa o mouse por cima (hover).</p>
                            </td>
                        </tr>


                    </table>
                    </div>
                </div>
            </div>

            <!-- Aba Textos -->
            <div class="cpr-tab-content" id="tab-texts" style="display: none;">
                <div class="cpr-settings-grid">
                    <div class="cpr-card">
                        <h2>Personalização de Textos</h2>
                    
                    <table class="form-table">
                        <tr>
                            <th scope="row">
                                <label for="title">Título Principal</label>
                            </th>
                            <td>
                                <input type="text" name="settings[title]" id="title" value="<?php echo esc_attr($settings['title']); ?>" class="regular-text">
                                <p class="description">Título que aparece no topo do formulário.</p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="subtitle">Subtítulo</label>
                            </th>
                            <td>
                                <textarea name="settings[subtitle]" id="subtitle" rows="3" class="large-text"><?php echo esc_textarea($settings['subtitle']); ?></textarea>
                                <p class="description">Texto explicativo abaixo do título.</p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="button_text">Texto do Botão</label>
                            </th>
                            <td>
                                <input type="text" name="settings[button_text]" id="button_text" value="<?php echo esc_attr($settings['button_text']); ?>" class="regular-text">
                                <p class="description">Texto do botão de envio.</p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="back_to_login_text">Texto "Voltar ao Login"</label>
                            </th>
                            <td>
                                <input type="text" name="settings[back_to_login_text]" id="back_to_login_text" value="<?php echo esc_attr($settings['back_to_login_text']); ?>" class="regular-text">
                                <p class="description">Texto do link para voltar ao login.</p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="success_message">Mensagem de Sucesso</label>
                            </th>
                            <td>
                                <textarea name="settings[success_message]" id="success_message" rows="3" class="large-text"><?php echo esc_textarea($settings['success_message']); ?></textarea>
                                <p class="description">Mensagem exibida quando o e-mail de reset é enviado com sucesso.</p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="input_placeholder">Placeholder do Campo E-mail</label>
                            </th>
                            <td>
                                <input type="text" name="settings[input_placeholder]" id="input_placeholder" value="<?php echo esc_attr($settings['input_placeholder']); ?>" class="regular-text">
                                <p class="description">Texto de exemplo que aparece dentro do campo de e-mail.</p>
                            </td>
                        </tr>
                    </table>
                    </div>
                </div>
            </div>

            <!-- Aba Email -->
            <div class="cpr-tab-content" id="tab-email" style="display: none;">
                <div class="cpr-settings-grid">
                    <!-- Configurações -->
                    <div class="cpr-card">
                        <h2>Configurações Gerais</h2>
                            
                            <table class="form-table">
                                <tr>
                                    <th scope="row">
                                        <label for="email_enabled">Personalizar Email</label>
                                    </th>
                                    <td>
                                        <label>
                                            <input type="checkbox" name="settings[email_enabled]" id="email_enabled" value="1" <?php checked($settings['email_enabled'], true); ?>>
                                            Usar template personalizado para emails de redefinição de senha
                                        </label>
                                        <p class="description">Quando ativado, usará o template moderno em vez do email padrão do WordPress.</p>
                                    </td>
                                </tr>
                                
                                <tr class="cpr-conditional" data-condition="email_enabled" data-value="1">
                                    <th scope="row">
                                        <label for="email_subject">Assunto do Email</label>
                                    </th>
                                    <td>
                                        <input type="text" name="settings[email_subject]" id="email_subject" value="<?php echo esc_attr($settings['email_subject']); ?>" class="regular-text cpr-preview-trigger">
                                        <p class="description">Assunto que aparecerá no email de redefinição de senha.</p>
                                    </td>
                                </tr>
                                
                                <tr class="cpr-conditional" data-condition="email_enabled" data-value="1">
                                    <th scope="row">
                                        <label for="email_from_name">Nome do Remetente</label>
                                    </th>
                                    <td>
                                        <input type="text" name="settings[email_from_name]" id="email_from_name" value="<?php echo esc_attr($settings['email_from_name']); ?>" class="regular-text">
                                        <p class="description">Nome que aparecerá como remetente do email.</p>
                                    </td>
                                </tr>
                                
                                <tr class="cpr-conditional" data-condition="email_enabled" data-value="1">
                                    <th scope="row">
                                        <label for="email_from_email">Email do Remetente</label>
                                    </th>
                                    <td>
                                        <input type="email" name="settings[email_from_email]" id="email_from_email" value="<?php echo esc_attr($settings['email_from_email']); ?>" class="regular-text">
                                        <p class="description">Endereço de email que aparecerá como remetente.</p>
                                    </td>
                                </tr>
                            </table>
                    </div>

                    <div class="cpr-card cpr-conditional" data-condition="email_enabled" data-value="1">
                            <h2>Aparência</h2>
                            
                            <table class="form-table">



                                <tr>
                                    <th scope="row">
                                        <label for="email_header_color">Cor do Cabeçalho</label>
                                    </th>
                                    <td>
                                        <input type="text" name="settings[email_header_color]" id="email_header_color" value="<?php echo esc_attr($settings['email_header_color']); ?>" class="cpr-color-picker cpr-preview-trigger">
                                        <p class="description">Cor de fundo do cabeçalho do email.</p>
                                    </td>
                                </tr>

                                <tr>
                                    <th scope="row">
                                        <label for="email_header_pattern">Padrão do Cabeçalho</label>
                                    </th>
                                    <td>
                                        <select name="settings[email_header_pattern]" id="email_header_pattern" class="cpr-preview-trigger">
                                            <option value="none" <?php selected($settings['email_header_pattern'], 'none'); ?>>Nenhum</option>
                                            <option value="floating_dots" <?php selected($settings['email_header_pattern'], 'floating_dots'); ?>>Pontos Flutuantes</option>
                                            <option value="organic_shapes" <?php selected($settings['email_header_pattern'], 'organic_shapes'); ?>>Formas Orgânicas</option>
                                            <option value="geometric_minimal" <?php selected($settings['email_header_pattern'], 'geometric_minimal'); ?>>Geométrico Minimal</option>
                                            <option value="flowing_lines" <?php selected($settings['email_header_pattern'], 'flowing_lines'); ?>>Linhas Fluidas</option>
                                            <option value="scattered_elements" <?php selected($settings['email_header_pattern'], 'scattered_elements'); ?>>Elementos Espalhados</option>
                                            <option value="modern_grid" <?php selected($settings['email_header_pattern'], 'modern_grid'); ?>>Grade Moderna</option>
                                            <option value="abstract_art" <?php selected($settings['email_header_pattern'], 'abstract_art'); ?>>Arte Abstrata</option>
                                        </select>
                                        <button type="button" id="regenerate-pattern" class="cpr-regenerate-btn" style="margin-left: 10px;">
                                            <span class="dashicons dashicons-update"></span>
                                            Regenerar Pattern
                                        </button>
                                        <p class="description">Padrão de fundo criativo e moderno para o cabeçalho do email. Cada padrão é gerado aleatoriamente para um visual único. Use o botão "Regenerar Pattern" para criar uma nova variação.</p>
                                    </td>
                                </tr>


                                <tr>
                                    <th scope="row">
                                        <label for="email_button_color">Cor do Botão</label>
                                    </th>
                                    <td>
                                        <input type="text" name="settings[email_button_color]" id="email_button_color" value="<?php echo esc_attr($settings['email_button_color']); ?>" class="cpr-color-picker cpr-preview-trigger">
                                        <p class="description">Cor do botão "Redefinir Senha" no email.</p>
                                    </td>
                                </tr>
                            </table>
                    </div>

                    <div class="cpr-card cpr-conditional" data-condition="email_enabled" data-value="1">
                            <h2>Textos Personalizáveis</h2>
                            
                            <table class="form-table">
                                <tr>
                                    <th scope="row">
                                        <label for="email_greeting">Saudação</label>
                                    </th>
                                    <td>
                                        <input type="text" name="settings[email_greeting]" id="email_greeting" value="<?php echo esc_attr($settings['email_greeting']); ?>" class="regular-text cpr-preview-trigger">
                                        <p class="description">Saudação inicial (ex: "Olá", "Oi", "Prezado(a)").</p>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <th scope="row">
                                        <label for="email_main_text">Texto Principal</label>
                                    </th>
                                    <td>
                                        <textarea name="settings[email_main_text]" id="email_main_text" rows="3" class="large-text cpr-preview-trigger"><?php echo esc_textarea($settings['email_main_text']); ?></textarea>
                                        <p class="description">Texto principal explicando a solicitação. Use {site_name} para o nome do site.</p>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <th scope="row">
                                        <label for="email_instruction_text">Texto de Instrução</label>
                                    </th>
                                    <td>
                                        <textarea name="settings[email_instruction_text]" id="email_instruction_text" rows="2" class="large-text cpr-preview-trigger"><?php echo esc_textarea($settings['email_instruction_text']); ?></textarea>
                                        <p class="description">Instrução sobre como proceder com o botão.</p>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <th scope="row">
                                        <label for="email_button_text">Texto do Botão</label>
                                    </th>
                                    <td>
                                        <input type="text" name="settings[email_button_text]" id="email_button_text" value="<?php echo esc_attr($settings['email_button_text']); ?>" class="regular-text cpr-preview-trigger">
                                        <p class="description">Texto que aparecerá no botão principal.</p>
                                    </td>
                                </tr>
                                


                                <tr>
                                    <th scope="row">
                                        <label for="email_footer_text">Texto do Rodapé</label>
                                    </th>
                                    <td>
                                        <textarea name="settings[email_footer_text]" id="email_footer_text" rows="2" class="large-text cpr-preview-trigger"><?php echo esc_textarea($settings['email_footer_text']); ?></textarea>
                                        <p class="description">Texto que aparecerá no rodapé do email.</p>
                                    </td>
                                </tr>
                            </table>
                    </div>

                    <div class="cpr-card cpr-conditional" data-condition="email_enabled" data-value="1">
                            <h2>Teste de Email</h2>
                            <p>Envie um email de teste para verificar como ficará o template:</p>
                            
                            <table class="form-table">
                                <tr>
                                    <th scope="row">
                                        <label for="test_email">Email de Teste</label>
                                    </th>
                                    <td>
                                        <input type="email" id="test_email" value="<?php echo esc_attr(wp_get_current_user()->user_email); ?>" class="regular-text">
                                        <button type="button" class="button" id="send-test-email">
                                            <i class="dashicons dashicons-email-alt"></i>
                                            Enviar Teste
                                        </button>
                                        <p class="description">Um email de exemplo será enviado para este endereço.</p>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- Preview -->
                    <div class="cpr-card cpr-conditional" data-condition="email_enabled" data-value="1">
                            <h2>Preview do Email</h2>
                            <p>Visualização em tempo real do template de email:</p>
                            
                            <div class="cpr-email-preview-container">
                                <iframe id="email-preview-frame" src="about:blank" frameborder="0"></iframe>
                            </div>
                            
                            <div class="cpr-preview-actions">
                                <button type="button" class="button" id="refresh-preview">
                                    <i class="dashicons dashicons-update"></i>
                                    Atualizar Preview
                                </button>
                                <button type="button" class="button" id="preview-mobile">
                                    <i class="dashicons dashicons-smartphone"></i>
                                    Visualização Mobile
                                </button>
                                <button type="button" class="button" id="preview-desktop">
                                    <i class="dashicons dashicons-desktop"></i>
                                    Visualização Desktop
                                </button>
                            </div>
                    </div>
                </div>
            </div>

            <!-- Aba Avançado -->
            <div class="cpr-tab-content" id="tab-advanced" style="display: none;">
                <div class="cpr-settings-grid">
                    <div class="cpr-card">
                        <h2>Configurações Avançadas</h2>
                    
                    <table class="form-table">
                        <tr>
                            <th scope="row">CSS Personalizado</th>
                            <td>
                                <textarea name="settings[custom_css]" id="custom_css" rows="10" class="large-text code"><?php echo esc_textarea($settings['custom_css'] ?? ''); ?></textarea>
                                <p class="description">CSS personalizado que será aplicado à página de reset de senha.</p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">JavaScript Personalizado</th>
                            <td>
                                <textarea name="settings[custom_js]" id="custom_js" rows="10" class="large-text code"><?php echo esc_textarea($settings['custom_js'] ?? ''); ?></textarea>
                                <p class="description">JavaScript personalizado (sem as tags &lt;script&gt;).</p>
                            </td>
                        </tr>
                    </table>
                    </div>
                </div>
            </div>

            <!-- Aba Preview -->
            <div class="cpr-tab-content" id="tab-preview" style="display: none;">
                <div class="cpr-settings-grid">
                    <div class="cpr-card">
                        <h2>Visualização</h2>
                        <p>Clique no link abaixo para visualizar a página de reset de senha:</p>

                        <div class="cpr-preview-container">
                            <a href="<?php echo wp_login_url() . '?action=lostpassword'; ?>"
                               target="_blank"
                               class="cpr-preview-link">
                                <span class="dashicons dashicons-external"></span>
                                Visualizar Página de Reset
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Botões de ação -->
            <div class="cpr-admin-actions">
                <div class="cpr-actions-wrapper">
                    <button type="submit" class="cpr-btn-primary" id="cpr-save-settings">
                        <span class="cpr-btn-icon dashicons dashicons-yes"></span>
                        <span class="cpr-btn-text">Salvar Configurações</span>
                    </button>

                    <button type="button" class="cpr-btn-secondary" id="cpr-reset-settings">
                        <span class="cpr-btn-icon dashicons dashicons-update"></span>
                        <span class="cpr-btn-text">Restaurar Padrões</span>
                    </button>

                    <div class="cpr-admin-status" id="cpr-status" style="display: none;"></div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- CSS Minimalista e Clean -->
<style>
/* Custom Password Reset Admin Styles - Minimal & Clean */
.cpr-admin-wrap {
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    background: #fafafa !important;
    min-height: 100vh !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

/* Header minimalista */
.cpr-admin-header {
    background: #ffffff !important;
    border: none !important;
    border-bottom: 1px solid #e5e5e5 !important;
    padding: 0 !important;
    margin: 0 !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) !important;
}

.cpr-header-content {
    max-width: 1200px !important;
    margin: 0 auto !important;
    padding: 32px 40px !important;
}

.cpr-header-title {
    display: flex !important;
    align-items: center !important;
    gap: 16px !important;
}

.cpr-icon {
    width: 48px !important;
    height: 48px !important;
    background: #f8f9fa !important;
    border-radius: 8px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border: 1px solid #e9ecef !important;
}

.cpr-icon .dashicons {
    font-size: 20px !important;
    color: #495057 !important;
    width: 20px !important;
    height: 20px !important;
}

.cpr-title-text h1 {
    margin: 0 0 4px 0 !important;
    font-size: 24px !important;
    font-weight: 600 !important;
    color: #212529 !important;
    letter-spacing: -0.025em !important;
}

.cpr-title-text p {
    margin: 0 !important;
    font-size: 14px !important;
    color: #6c757d !important;
    font-weight: 400 !important;
}

/* Container principal */
.cpr-admin-container {
    background: transparent !important;
    border: none !important;
    margin: 0 !important;
    box-shadow: none !important;
}

/* Navigation Tabs - Design minimalista */
.cpr-nav-tabs {
    background: #ffffff !important;
    border-bottom: 1px solid #e5e5e5 !important;
    margin: 0 !important;
    padding: 0 !important;
    position: sticky !important;
    top: 32px !important;
    z-index: 100 !important;
}

.cpr-nav-wrapper {
    display: flex !important;
    max-width: 1200px !important;
    margin: 0 auto !important;
    padding: 0 40px !important;
    gap: 0 !important;
}

.cpr-nav-tab {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    padding: 16px 20px !important;
    text-decoration: none !important;
    color: #6c757d !important;
    font-weight: 500 !important;
    font-size: 14px !important;
    border-bottom: 2px solid transparent !important;
    transition: all 0.15s ease !important;
    background: none !important;
    border-left: none !important;
    border-right: none !important;
    border-top: none !important;
}

.cpr-nav-tab:hover {
    color: #495057 !important;
    background: #f8f9fa !important;
    text-decoration: none !important;
}

.cpr-nav-tab.cpr-nav-tab-active {
    color: #212529 !important;
    border-bottom-color: #212529 !important;
    background: #ffffff !important;
}

.cpr-tab-icon {
    font-size: 14px !important;
    line-height: 1 !important;
    opacity: 0.8 !important;
}

.cpr-tab-text {
    font-weight: 500 !important;
}

/* Tab Content - Layout minimalista */
.cpr-tab-content {
    max-width: 1200px !important;
    margin: 0 auto !important;
    padding: 32px 40px 40px !important;
    min-height: 500px !important;
    background: transparent !important;
}

/* Grid de configurações */
.cpr-settings-grid {
    display: block !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Layout específico para aba de aparência */
#tab-appearance .cpr-settings-grid {
    display: grid !important;
    grid-template-columns: 1fr 1fr !important;
    gap: 24px !important;
    align-items: start !important;
}

#tab-appearance .cpr-card {
    margin-bottom: 0 !important;
}

#tab-appearance .cpr-card h2 {
    margin-top: 0 !important;
    margin-bottom: 20px !important;
    padding-bottom: 12px !important;
    border-bottom: 2px solid #f1f1f1 !important;
    color: #495057 !important;
    font-size: 18px !important;
}

/* Responsividade para grid em telas grandes */
@media (min-width: 1400px) {
    .cpr-settings-grid:not(#tab-appearance .cpr-settings-grid) {
        display: grid !important;
        grid-template-columns: 1fr 1fr !important;
        gap: 20px !important;
        align-items: start !important;
    }
}

/* Responsividade para abas com grid */
@media (max-width: 1200px) {
    #tab-appearance .cpr-settings-grid,
    #tab-email .cpr-settings-grid {
        display: block !important;
        grid-template-columns: none !important;
    }

    #tab-appearance .cpr-card,
    #tab-email .cpr-card {
        margin-bottom: 20px !important;
    }
}

.cpr-setting-group {
    background: #ffffff !important;
    border-radius: 6px !important;
    padding: 24px !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
    border: 1px solid #e5e5e5 !important;
}

.cpr-setting-header {
    margin-bottom: 20px !important;
    padding-bottom: 12px !important;
    border-bottom: 1px solid #f0f0f0 !important;
}

.cpr-setting-header h3 {
    margin: 0 !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    color: #212529 !important;
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
}

/* Animação para o botão de regenerar */
.dashicons.spin {
    animation: spin 1s linear infinite !important;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

#regenerate-pattern {
    vertical-align: top !important;
}

/* Setting Items - Design minimalista */
.cpr-setting-item {
    margin-bottom: 20px !important;
}

.cpr-setting-item:last-child {
    margin-bottom: 0 !important;
}

.cpr-setting-label {
    display: block !important;
    font-weight: 500 !important;
    font-size: 13px !important;
    color: #495057 !important;
    margin-bottom: 6px !important;
}

.cpr-setting-desc {
    display: block !important;
    font-size: 12px !important;
    color: #6c757d !important;
    margin-top: 4px !important;
    line-height: 1.4 !important;
}

/* Inputs minimalistas */
.cpr-input, .cpr-select {
    width: 100% !important;
    max-width: 400px !important;
    padding: 10px 12px !important;
    border: 1px solid #ced4da !important;
    border-radius: 4px !important;
    font-size: 14px !important;
    background: #ffffff !important;
    transition: border-color 0.15s ease !important;
    color: #495057 !important;
}

.cpr-input:focus, .cpr-select:focus {
    outline: none !important;
    border-color: #80bdff !important;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1) !important;
}

.cpr-input::placeholder {
    color: #adb5bd !important;
}

/* Input Group */
.cpr-input-group {
    display: flex !important;
    gap: 12px !important;
    align-items: center !important;
}

.cpr-input-group .cpr-input {
    flex: 1 !important;
}

/* Toggle Switch minimalista */
.cpr-toggle-wrapper {
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
}

.cpr-toggle {
    position: relative !important;
    display: inline-block !important;
    width: 44px !important;
    height: 22px !important;
}

.cpr-toggle input {
    opacity: 0 !important;
    width: 0 !important;
    height: 0 !important;
}

.cpr-toggle-slider {
    position: absolute !important;
    cursor: pointer !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background-color: #dee2e6 !important;
    transition: 0.2s !important;
    border-radius: 22px !important;
}

.cpr-toggle-slider:before {
    position: absolute !important;
    content: "" !important;
    height: 16px !important;
    width: 16px !important;
    left: 3px !important;
    bottom: 3px !important;
    background-color: white !important;
    transition: 0.2s !important;
    border-radius: 50% !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.15) !important;
}

.cpr-toggle input:checked + .cpr-toggle-slider {
    background-color: #007bff !important;
}

.cpr-toggle input:checked + .cpr-toggle-slider:before {
    transform: translateX(22px) !important;
}

.cpr-toggle-text {
    font-size: 13px !important;
    color: #495057 !important;
    font-weight: 500 !important;
}

/* Botões minimalistas */
.cpr-btn-primary, .cpr-btn-secondary {
    display: inline-flex !important;
    align-items: center !important;
    gap: 6px !important;
    padding: 10px 20px !important;
    border-radius: 4px !important;
    font-weight: 500 !important;
    font-size: 14px !important;
    text-decoration: none !important;
    transition: all 0.15s ease !important;
    cursor: pointer !important;
    border: 1px solid transparent !important;
    outline: none !important;
}

.cpr-btn-primary {
    background: #007bff !important;
    color: white !important;
    border-color: #007bff !important;
}

.cpr-btn-primary:hover {
    background: #0056b3 !important;
    border-color: #0056b3 !important;
    color: white !important;
}

.cpr-btn-secondary {
    background: #ffffff !important;
    color: #495057 !important;
    border: 1px solid #ced4da !important;
}

.cpr-btn-secondary:hover {
    background: #f8f9fa !important;
    border-color: #adb5bd !important;
    color: #495057 !important;
}

.cpr-btn-icon {
    font-size: 14px !important;
    line-height: 1 !important;
}

/* Action Buttons Container */
.cpr-admin-actions {
    background: #ffffff !important;
    border-top: 1px solid #e5e5e5 !important;
    padding: 0 !important;
    position: sticky !important;
    bottom: 0 !important;
    z-index: 50 !important;
}

.cpr-actions-wrapper {
    max-width: 1200px !important;
    margin: 0 auto !important;
    padding: 24px 40px !important;
    display: flex !important;
    align-items: center !important;
    gap: 12px !important;
}

/* Conditional Fields */
.cpr-conditional {
    display: none !important;
    opacity: 0 !important;
    transition: opacity 0.3s ease !important;
}

.cpr-conditional.show {
    display: block !important;
    opacity: 1 !important;
}

/* Status Messages minimalistas */
.cpr-admin-status {
    display: inline-flex !important;
    align-items: center !important;
    gap: 6px !important;
    padding: 8px 12px !important;
    border-radius: 4px !important;
    font-weight: 500 !important;
    font-size: 13px !important;
    animation: slideIn 0.2s ease !important;
}

.cpr-admin-status.success {
    background: #d4edda !important;
    color: #155724 !important;
    border: 1px solid #c3e6cb !important;
}

.cpr-admin-status.error {
    background: #f8d7da !important;
    color: #721c24 !important;
    border: 1px solid #f5c6cb !important;
}

.cpr-admin-status::before {
    font-size: 14px !important;
    line-height: 1 !important;
}

.cpr-admin-status.success::before {
    content: '✓' !important;
}

.cpr-admin-status.error::before {
    content: '✗' !important;
}

@keyframes slideIn {
    from {
        opacity: 0 !important;
        transform: translateY(-10px) !important;
    }
    to {
        opacity: 1 !important;
        transform: translateY(0) !important;
    }
}

/* Responsivo */
@media (max-width: 768px) {
    .cpr-header-content {
        padding: 20px !important;
    }

    .cpr-header-title {
        flex-direction: column !important;
        text-align: center !important;
        gap: 12px !important;
    }

    .cpr-nav-wrapper {
        padding: 0 20px !important;
        flex-wrap: wrap !important;
    }

    .cpr-nav-tab {
        flex: 1 !important;
        min-width: 0 !important;
        justify-content: center !important;
        padding: 12px 8px !important;
        font-size: 12px !important;
    }

    .cpr-tab-text {
        display: none !important;
    }

    .cpr-tab-content {
        padding: 20px !important;
    }

    .cpr-setting-group {
        padding: 16px !important;
    }

    .cpr-actions-wrapper {
        padding: 16px 20px !important;
        flex-direction: column !important;
        gap: 10px !important;
    }

    .cpr-btn-primary, .cpr-btn-secondary {
        width: 100% !important;
        justify-content: center !important;
    }

    .cpr-input, .cpr-select {
        max-width: 100% !important;
    }

    .cpr-input-group {
        flex-direction: column !important;
        align-items: stretch !important;
    }
}

/* Estilos específicos para outras abas */
.cpr-card {
    background: #ffffff !important;
    border-radius: 6px !important;
    padding: 24px !important;
    margin-bottom: 20px !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
    border: 1px solid #e5e5e5 !important;
    width: 100% !important;
    box-sizing: border-box !important;
}

.cpr-card h2 {
    margin: 0 0 20px 0 !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    color: #212529 !important;
    padding-bottom: 12px !important;
    border-bottom: 1px solid #f0f0f0 !important;
}

.form-table {
    background: transparent !important;
    border: none !important;
    width: 100% !important;
    border-collapse: collapse !important;
    margin: 0 !important;
}

.form-table th {
    padding: 16px 20px 16px 0 !important;
    font-weight: 500 !important;
    font-size: 14px !important;
    color: #495057 !important;
    width: 200px !important;
    vertical-align: middle !important;
    text-align: left !important;
    border-bottom: 1px solid #f8f9fa !important;
}

.form-table td {
    padding: 16px 0 !important;
    vertical-align: middle !important;
    border-bottom: 1px solid #f8f9fa !important;
}

.form-table input[type="text"],
.form-table input[type="url"],
.form-table input[type="email"],
.form-table textarea,
.form-table select {
    width: 100% !important;
    max-width: 400px !important;
    padding: 12px 16px !important;
    border: 1px solid #ddd !important;
    border-radius: 6px !important;
    font-size: 14px !important;
    background: #ffffff !important;
    color: #495057 !important;
    transition: all 0.2s ease !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
}

.form-table input[type="text"]:focus,
.form-table input[type="url"]:focus,
.form-table input[type="email"]:focus,
.form-table textarea:focus,
.form-table select:focus {
    outline: none !important;
    border-color: #007cba !important;
    box-shadow: 0 0 0 3px rgba(0, 124, 186, 0.1) !important;
}

.form-table textarea {
    min-height: 80px !important;
    resize: vertical !important;
}

.form-table .description {
    font-size: 12px !important;
    color: #6c757d !important;
    margin-top: 4px !important;
    line-height: 1.4 !important;
}

/* Color picker específico */
.cpr-color-picker-wrapper {
    display: flex !important;
    align-items: center !important;
    gap: 12px !important;
    margin-bottom: 8px !important;
    flex-wrap: wrap !important;
}

.wp-picker-container {
    margin: 0 !important;
    display: inline-block !important;
    vertical-align: top !important;
}

.wp-picker-container .wp-color-result {
    border-radius: 6px !important;
    border: 2px solid #ddd !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
    height: 40px !important;
    width: 80px !important;
    padding: 0 !important;
    margin: 0 !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    position: relative !important;
    background-size: cover !important;
    background-position: center !important;
    overflow: hidden !important;
}

/* Forçar exibição da cor */
.wp-picker-container .wp-color-result:before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background: inherit !important;
    z-index: 1 !important;
}

/* Remover gradiente padrão do WordPress */
.wp-picker-container .wp-color-result {
    background-image: none !important;
}

/* Garantir que a cor seja visível */
.wp-picker-container .wp-color-result-text {
    display: none !important;
}

.wp-picker-container .wp-color-result abbr {
    display: none !important;
}

/* Sobrescrever todos os estilos do WordPress para color picker */
.cpr-color-picker-wrapper .wp-picker-container .wp-color-result {
    background: var(--picker-color, #cccccc) !important;
    background-image: none !important;
    background-size: auto !important;
    background-position: 0 0 !important;
    background-repeat: no-repeat !important;
}

/* Força a cor usando CSS custom property */
.cpr-color-picker-wrapper .wp-picker-container .wp-color-result[style*="background-color"] {
    background: var(--picker-color) !important;
}

.wp-picker-container .wp-color-result:hover,
.wp-picker-container .wp-color-result:focus {
    border-color: #007cba !important;
    box-shadow: 0 0 0 3px rgba(0, 124, 186, 0.1) !important;
}

.wp-picker-container .wp-color-result:after {
    display: none !important;
}

.wp-picker-container .wp-color-result .wp-color-result-text {
    display: none !important;
}

.cpr-color-value {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
    font-size: 12px !important;
    color: #495057 !important;
    background: #f8f9fa !important;
    padding: 10px 12px !important;
    border-radius: 4px !important;
    border: 1px solid #e5e5e5 !important;
    min-width: 90px !important;
    text-align: center !important;
    user-select: all !important;
    font-weight: 500 !important;
    letter-spacing: 0.5px !important;
}

/* Esconder o input original do color picker */
.wp-picker-container .wp-color-picker {
    display: none !important;
}

/* Ajustes específicos para aba de aparência */
#tab-appearance .form-table {
    margin-top: 0 !important;
}

#tab-appearance .form-table th {
    width: 180px !important;
    padding-right: 16px !important;
}

#tab-appearance .cpr-color-picker-wrapper {
    margin-bottom: 4px !important;
}

#tab-appearance .form-table .description {
    margin-top: 6px !important;
    font-size: 12px !important;
    line-height: 1.4 !important;
}

/* Layout específico para aba de textos */
#tab-texts .cpr-settings-grid {
    display: block !important;
    grid-template-columns: none !important;
}

#tab-texts .cpr-card {
    width: 100% !important;
    max-width: none !important;
    grid-column: 1 / -1 !important;
    margin-bottom: 0 !important;
}

#tab-texts .form-table {
    margin-top: 0 !important;
}

#tab-texts .form-table th {
    width: 200px !important;
    padding-right: 20px !important;
}

#tab-texts .form-table td {
    padding-right: 0 !important;
}

#tab-texts .form-table input[type="text"],
#tab-texts .form-table textarea {
    max-width: 600px !important;
}

#tab-texts .form-table textarea {
    min-height: 100px !important;
    resize: vertical !important;
}

/* Layout específico para aba de email */
#tab-email .cpr-settings-grid {
    display: grid !important;
    grid-template-columns: 1fr 1fr !important;
    gap: 24px !important;
    align-items: start !important;
}

/* Layout específico para aba avançado */
#tab-advanced .cpr-settings-grid {
    display: block !important;
    grid-template-columns: none !important;
}

#tab-advanced .cpr-card {
    width: 100% !important;
    max-width: none !important;
}

/* Layout específico para aba preview */
#tab-preview .cpr-settings-grid {
    display: block !important;
    grid-template-columns: none !important;
}

#tab-preview .cpr-card {
    width: 100% !important;
    max-width: none !important;
}

/* Melhorar espaçamento entre linhas na aba de aparência */
#tab-appearance .form-table tr {
    border-bottom: 1px solid #f1f1f1 !important;
}

#tab-appearance .form-table tr:last-child {
    border-bottom: none !important;
}

/* Estilo para os títulos dos cards */
#tab-appearance .cpr-card h2 {
    font-size: 16px !important;
    font-weight: 600 !important;
    color: #2c3e50 !important;
    margin: 0 0 16px 0 !important;
    padding: 0 0 8px 0 !important;
    border-bottom: 2px solid #e9ecef !important;
}
}

.wp-picker-container .wp-color-result:after {
    font-size: 12px !important;
    line-height: 30px !important;
}

/* Checkboxes e radios */
.form-table input[type="checkbox"],
.form-table input[type="radio"] {
    margin-right: 8px !important;
}

/* Botões dentro das abas */
.cpr-card .button,
.cpr-card .button-primary,
.cpr-card .button-secondary {
    padding: 8px 16px !important;
    border-radius: 4px !important;
    font-size: 13px !important;
    font-weight: 500 !important;
    text-decoration: none !important;
    border: 1px solid #ced4da !important;
    background: #ffffff !important;
    color: #495057 !important;
    transition: all 0.15s ease !important;
}

.cpr-card .button-primary {
    background: #007bff !important;
    color: white !important;
    border-color: #007bff !important;
}

.cpr-card .button:hover,
.cpr-card .button-secondary:hover {
    background: #f8f9fa !important;
    border-color: #adb5bd !important;
}

.cpr-card .button-primary:hover {
    background: #0056b3 !important;
    border-color: #0056b3 !important;
}

/* Preview específico */
.cpr-preview-container {
    background: #f8f9fa !important;
    border: 1px solid #e5e5e5 !important;
    border-radius: 4px !important;
    padding: 20px !important;
    margin-top: 12px !important;
}

/* Container de preview */
.cpr-preview-container {
    margin: 20px 0 !important;
    text-align: center !important;
    padding: 20px !important;
    background: #f8f9fa !important;
    border-radius: 8px !important;
    border: 1px solid #e5e5e5 !important;
}

.cpr-preview-link {
    display: inline-flex !important;
    align-items: center !important;
    gap: 8px !important;
    padding: 14px 24px !important;
    background: linear-gradient(135deg, #007cba 0%, #005a87 100%) !important;
    color: #ffffff !important;
    text-decoration: none !important;
    border-radius: 8px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 4px rgba(0, 124, 186, 0.2) !important;
}

.cpr-preview-link:hover {
    background: linear-gradient(135deg, #005a87 0%, #004066 100%) !important;
    color: #ffffff !important;
    text-decoration: none !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 12px rgba(0, 124, 186, 0.3) !important;
}

.cpr-preview-link:active {
    transform: translateY(0) !important;
    box-shadow: 0 2px 4px rgba(0, 124, 186, 0.2) !important;
}

.cpr-preview-link .dashicons {
    font-size: 16px !important;
    width: 16px !important;
    height: 16px !important;
}

/* Melhorias específicas para campos */
.form-table tr {
    border-bottom: 1px solid #f8f9fa !important;
}

.form-table tr:last-child {
    border-bottom: none !important;
}

/* Campos condicionais */
.cpr-conditional {
    opacity: 0.6 !important;
    transition: opacity 0.2s ease !important;
}

.cpr-conditional.show {
    opacity: 1 !important;
}

/* Melhorias para textarea */
.form-table textarea.code {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
    font-size: 13px !important;
    line-height: 1.4 !important;
    background: #f8f9fa !important;
}

/* Estilos para labels */
.form-table th label {
    font-weight: 500 !important;
    color: #495057 !important;
    margin: 0 !important;
    display: block !important;
}

.form-table td label {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    font-size: 14px !important;
    color: #495057 !important;
    cursor: pointer !important;
    margin: 0 !important;
}

.form-table input[type="checkbox"] + span,
.form-table input[type="radio"] + span {
    user-select: none !important;
}

/* Melhorias para checkboxes e radio buttons */
.form-table input[type="checkbox"],
.form-table input[type="radio"] {
    margin-right: 8px !important;
    transform: scale(1.1) !important;
}

/* Estilos para botões especiais */
.cpr-regenerate-btn {
    display: inline-flex !important;
    align-items: center !important;
    gap: 6px !important;
    padding: 8px 12px !important;
    background: #f8f9fa !important;
    border: 1px solid #ddd !important;
    border-radius: 4px !important;
    color: #495057 !important;
    text-decoration: none !important;
    font-size: 13px !important;
    transition: all 0.2s ease !important;
}

.cpr-regenerate-btn:hover {
    background: #e9ecef !important;
    border-color: #adb5bd !important;
    color: #495057 !important;
    text-decoration: none !important;
}

.cpr-regenerate-btn .dashicons {
    font-size: 16px !important;
    width: 16px !important;
    height: 16px !important;
}
</style>

<!-- JavaScript da página de administração -->
<script>
jQuery(document).ready(function($) {
    // Função para inicializar color pickers com wrapper
    function initColorPickers() {
        $('.cpr-color-picker').each(function() {
            var $input = $(this);
            var $wrapper = $input.closest('.cpr-color-picker-wrapper');
            var currentValue = $input.val() || $input.data('default-color') || '#000000';

            // Se já foi inicializado, pular
            if ($input.hasClass('wp-color-picker')) {
                return;
            }

            // Se não tem wrapper, criar um
            if ($wrapper.length === 0) {
                $input.wrap('<div class="cpr-color-picker-wrapper"></div>');
                $input.after('<span class="cpr-color-value">' + currentValue + '</span>');
                $wrapper = $input.closest('.cpr-color-picker-wrapper');
            }

            // Atualizar o valor mostrado
            $wrapper.find('.cpr-color-value').text(currentValue);

            // Inicializar o color picker do WordPress
            $input.wpColorPicker({
                defaultColor: currentValue,
                change: function(event, ui) {
                    var color = ui.color.toString();
                    $wrapper.find('.cpr-color-value').text(color);

                    // Forçar atualização visual
                    var $colorResult = $wrapper.find('.wp-color-result');
                    $colorResult.css({
                        'background-color': color,
                        'background-image': 'none'
                    });
                    $colorResult.attr('style', $colorResult.attr('style') + '; background-color: ' + color + ' !important; background-image: none !important;');

                    // Trigger change event para outros scripts
                    $input.trigger('colorchange', [color]);
                },
                clear: function() {
                    var defaultColor = $input.data('default-color') || '#000000';
                    $wrapper.find('.cpr-color-value').text(defaultColor);

                    // Forçar atualização visual
                    var $colorResult = $wrapper.find('.wp-color-result');
                    $colorResult.css({
                        'background-color': defaultColor,
                        'background-image': 'none'
                    });
                    $colorResult.attr('style', $colorResult.attr('style') + '; background-color: ' + defaultColor + ' !important; background-image: none !important;');

                    $input.trigger('colorchange', [defaultColor]);
                },
                palettes: ['#000000', '#ffffff', '#007cba', '#005a87', '#28a745', '#dc3545', '#ffc107', '#6f42c1']
            });

            // Forçar a cor inicial após inicialização
            setTimeout(function() {
                var $colorResult = $wrapper.find('.wp-color-result');
                if ($colorResult.length && currentValue) {
                    $colorResult.css({
                        'background-color': currentValue,
                        'background-image': 'none'
                    });
                    $colorResult.attr('style', $colorResult.attr('style') + '; background-color: ' + currentValue + ' !important; background-image: none !important;');
                }
            }, 100);
        });
    }

    // Função para forçar atualização visual dos color pickers
    function updateColorPickersVisual() {
        $('.cpr-color-picker').each(function() {
            var $input = $(this);
            var $wrapper = $input.closest('.cpr-color-picker-wrapper');
            var currentValue = $input.val();
            var $colorResult = $wrapper.find('.wp-color-result');

            if ($colorResult.length && currentValue) {
                // Usar CSS custom property
                $colorResult.css('--picker-color', currentValue);
                $colorResult.css({
                    'background-color': currentValue,
                    'background-image': 'none',
                    'background': currentValue
                });
                $colorResult.attr('style', $colorResult.attr('style') + '; --picker-color: ' + currentValue + '; background: ' + currentValue + ' !important; background-image: none !important;');
                $wrapper.find('.cpr-color-value').text(currentValue);
            }
        });
    }

    // Inicializar color pickers
    initColorPickers();

    // Garantir que os color pickers sejam inicializados e atualizados
    setTimeout(function() {
        initColorPickers();
        updateColorPickersVisual();
    }, 500);

    // Atualizar visual novamente após 1 segundo
    setTimeout(function() {
        updateColorPickersVisual();
    }, 1000);
    
    // Navegação por abas
    $('.cpr-nav-tab').on('click', function(e) {
        e.preventDefault();

        const tab = $(this).data('tab');

        // Atualizar abas ativas
        $('.cpr-nav-tab').removeClass('cpr-nav-tab-active');
        $(this).addClass('cpr-nav-tab-active');

        // Mostrar conteúdo da aba
        $('.cpr-tab-content').hide();
        $('#tab-' + tab).show();

        // Reinicializar color pickers na aba de aparência
        if (tab === 'appearance') {
            setTimeout(function() {
                initColorPickers();
                updateColorPickersVisual();
            }, 100);

            // Segunda tentativa após mais tempo
            setTimeout(function() {
                updateColorPickersVisual();
            }, 500);
        }

        // Atualizar URL
        window.location.hash = tab;
    });

    // Carregar aba da URL
    if (window.location.hash) {
        const hash = window.location.hash.substring(1);
        $('.cpr-nav-tab[data-tab="' + hash + '"]').click();
    }
    
    // Campos condicionais com animação suave
    function toggleConditionalFields() {
        $('.cpr-conditional').each(function() {
            const $row = $(this);
            const condition = $row.data('condition');
            const value = $row.data('value');
            const $field = $('#' + condition);

            let shouldShow = false;

            if ($field.is(':checkbox')) {
                shouldShow = ($field.is(':checked') && value == '1') || (!$field.is(':checked') && value == '0');
            } else {
                shouldShow = $field.val() == value;
            }

            if (shouldShow) {
                if (!$row.hasClass('show')) {
                    $row.slideDown(200).addClass('show');
                }
            } else {
                if ($row.hasClass('show')) {
                    $row.slideUp(200).removeClass('show');
                }
            }
        });
    }
    
    // Executar ao carregar e quando campos mudarem
    toggleConditionalFields();
    $('input, select').on('change', toggleConditionalFields);
    
    // Upload de imagem
    $('.cpr-upload-button').on('click', function(e) {
        e.preventDefault();
        
        const button = $(this);
        const targetField = button.data('target');
        
        const mediaUploader = wp.media({
            title: 'Selecionar Logo',
            button: {
                text: 'Usar esta imagem'
            },
            multiple: false
        });
        
        mediaUploader.on('select', function() {
            const attachment = mediaUploader.state().get('selection').first().toJSON();
            $('#' + targetField).val(attachment.url);
        });
        
        mediaUploader.open();
    });
    
    // Salvar configurações
    $('#cpr-settings-form').on('submit', function(e) {
        e.preventDefault();
        
        const $form = $(this);
        const $button = $('#cpr-save-settings');
        const $status = $('#cpr-status');
        
        $button.prop('disabled', true).find('.dashicons').addClass('spin');
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: $form.serialize() + '&action=cpr_save_settings',
            success: function(response) {
                if (response.success) {
                    $status.removeClass('error').addClass('success').text(response.data).show();
                } else {
                    $status.removeClass('success').addClass('error').text(response.data || 'Erro ao salvar').show();
                }
            },
            error: function() {
                $status.removeClass('success').addClass('error').text('Erro de conexão').show();
            },
            complete: function() {
                $button.prop('disabled', false).find('.dashicons').removeClass('spin');
                setTimeout(function() {
                    $status.fadeOut();
                }, 3000);
            }
        });
    });
    
    // Restaurar padrões
    $('#cpr-reset-settings').on('click', function() {
        if (confirm('Tem certeza que deseja restaurar todas as configurações para os valores padrão?')) {
            const $button = $(this);
            const $status = $('#cpr-status');
            
            $button.prop('disabled', true);
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'cpr_reset_settings',
                    nonce: $('input[name="nonce"]').val()
                },
                success: function(response) {
                    if (response.success) {
                        $status.removeClass('error').addClass('success').text(response.data).show();
                        setTimeout(function() {
                            location.reload();
                        }, 1000);
                    } else {
                        $status.removeClass('success').addClass('error').text(response.data || 'Erro ao restaurar').show();
                    }
                },
                error: function() {
                    $status.removeClass('success').addClass('error').text('Erro de conexão').show();
                },
                complete: function() {
                    $button.prop('disabled', false);
                    setTimeout(function() {
                        $status.fadeOut();
                    }, 3000);
                }
            });
        }
    });
    
    // Enviar email de teste
    $('#send-test-email').on('click', function() {
        const $button = $(this);
        const $status = $('#cpr-status');
        const testEmail = $('#test_email').val();
        
        if (!testEmail) {
            alert('Por favor, insira um endereço de email válido.');
            return;
        }
        
        $button.prop('disabled', true).find('.dashicons').addClass('spin');
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'cpr_send_test_email',
                email: testEmail,
                nonce: $('input[name="nonce"]').val()
            },
            success: function(response) {
                if (response.success) {
                    $status.removeClass('error').addClass('success').text('Email de teste enviado com sucesso!').show();
                } else {
                    $status.removeClass('success').addClass('error').text(response.data || 'Erro ao enviar email').show();
                }
            },
            error: function() {
                $status.removeClass('success').addClass('error').text('Erro de conexão').show();
            },
            complete: function() {
                $button.prop('disabled', false).find('.dashicons').removeClass('spin');
                setTimeout(function() {
                    $status.fadeOut();
                }, 3000);
            }
        });
    });
    
    // Preview do email em tempo real
    function updateEmailPreview() {
        if (!$('#email_enabled').is(':checked')) {
            return;
        }
        
        const settings = {
            email_header_color: $('#email_header_color').val(),
            email_header_pattern: $('#email_header_pattern').val(),
            email_button_color: $('#email_button_color').val(),
            email_greeting: $('#email_greeting').val(),
            email_main_text: $('#email_main_text').val(),
            email_instruction_text: $('#email_instruction_text').val(),
            email_button_text: $('#email_button_text').val(),
            email_footer_text: $('#email_footer_text').val()
        };
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'cpr_generate_email_preview',
                settings: settings,
                nonce: $('input[name="nonce"]').val()
            },
            success: function(response) {
                if (response.success) {
                    const iframe = document.getElementById('email-preview-frame');
                    const doc = iframe.contentDocument || iframe.contentWindow.document;
                    doc.open();
                    doc.write(response.data);
                    doc.close();
                }
            }
        });
    }
    
    // Atualizar preview quando campos mudarem
    $('.cpr-preview-trigger').on('input change', function() {
        clearTimeout(window.previewTimeout);
        window.previewTimeout = setTimeout(updateEmailPreview, 500);
    });
    
    // Botão de atualizar preview
    $('#refresh-preview').on('click', updateEmailPreview);
    
    // Alternar visualização mobile/desktop
    $('#preview-mobile').on('click', function() {
        $('.cpr-email-preview-container').addClass('mobile');
        $(this).addClass('button-primary').siblings().removeClass('button-primary');
    });
    
    $('#preview-desktop').on('click', function() {
        $('.cpr-email-preview-container').removeClass('mobile');
        $(this).addClass('button-primary').siblings().removeClass('button-primary');
    });
    
    // Inicializar preview quando a aba for carregada
    $('.nav-tab[data-tab="email"]').on('click', function() {
        setTimeout(function() {
            if ($('#email_enabled').is(':checked')) {
                updateEmailPreview();
            }
        }, 100);
    });
    
    // Botão para regenerar pattern
    $('#regenerate-pattern').on('click', function() {
        // Forçar regeneração do pattern adicionando timestamp
        const currentPattern = $('#email_header_pattern').val();
        if (currentPattern !== 'none') {
            // Trigger update do preview para regenerar o pattern
            updateEmailPreview();

            // Mostrar feedback visual
            const $btn = $(this);
            const originalText = $btn.html();
            $btn.html('<i class="dashicons dashicons-update spin"></i> Regenerando...');
            $btn.prop('disabled', true);

            setTimeout(function() {
                $btn.html(originalText);
                $btn.prop('disabled', false);
            }, 1000);
        }
    });

    // Atualizar preview quando email for habilitado
    $('#email_enabled').on('change', function() {
        if ($(this).is(':checked')) {
            setTimeout(updateEmailPreview, 100);
        }
    });

    // Forçar atualização dos color pickers quando a janela for focada
    $(window).on('focus', function() {
        setTimeout(updateColorPickersVisual, 200);
    });

    // Atualizar color pickers quando qualquer input mudar
    $(document).on('change', '.cpr-color-picker', function() {
        var $input = $(this);
        var $wrapper = $input.closest('.cpr-color-picker-wrapper');
        var $colorResult = $wrapper.find('.wp-color-result');
        var currentValue = $input.val();

        if ($colorResult.length && currentValue) {
            $colorResult.css('background-color', currentValue);
            $wrapper.find('.cpr-color-value').text(currentValue);
        }
    });
});
</script>
